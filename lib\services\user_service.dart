import '../models/user_model.dart';
import 'auth_service.dart';

class UserService {
  static final AuthService _authService = AuthService();

  // Get user by ID
  static Future<UserModel?> getUserById(String userId) async {
    return await _authService.getUserById(userId);
  }

  // Search users
  static Future<List<UserModel>> searchUsers(
    String query, {
    String? excludeUserId,
    int limit = 20,
  }) async {
    final users = await _authService.searchUsers(query: query, limit: limit);
    if (excludeUserId != null) {
      return users.where((user) => user.id != excludeUserId).toList();
    }
    return users;
  }

  // Follow/Unfollow user
  static Future<void> toggleFollow({
    required String currentUserId,
    required String targetUserId,
  }) async {
    return await _authService.toggleFollowUser(
      currentUserId: currentUserId,
      targetUserId: targetUserId,
    );
  }
}
